[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "gaapf-guidance-ai-agent"
version = "1.0.0"
description = "Guidance AI Agent for Python Framework - Adaptive Multi-Agent Learning System"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "GAAPF Development Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.10"
dependencies = [
    "langchain>=0.3.25",
    "langchain-core>=0.3.25",
    "langchain-community>=0.3.25",
    "langgraph>=0.4.7",
    "langsmith>=0.1.0",
    "langchain-google-genai>=2.0.0",
    "langchain-openai>=0.2.0",
    "langchain-anthropic>=0.2.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "tavily-python>=0.5.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "python-dotenv>=1.0.0",
    "click>=8.1.0",
    "rich>=13.7.0",
    "streamlit>=1.28.0",
    "pandas>=2.1.0",
    "numpy>=1.24.0",
    "json5>=0.9.0",
    "aiofiles>=23.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.9.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
]
viz = [
    "matplotlib>=3.7.0",
    "plotly>=5.17.0",
    "seaborn>=0.12.0",
]

[project.urls]
Homepage = "https://github.com/your-username/gaapf-guidance-ai-agent"
Documentation = "https://github.com/your-username/gaapf-guidance-ai-agent/blob/main/README.md"
Repository = "https://github.com/your-username/gaapf-guidance-ai-agent"
Issues = "https://github.com/your-username/gaapf-guidance-ai-agent/issues"

[project.scripts]
gaapf-cli = "pyframeworks_assistant.interfaces.cli.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py310"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
python_version = "3.10"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
asyncio_mode = "auto"
