"""
GAAPF - Guidance AI Agent for Python Framework

An Adaptive Multi-Agent Learning System for AI Framework Education
Built with LangChain, LangGraph, and advanced temporal optimization algorithms.
"""

__version__ = "1.0.0"
__author__ = "GAAPF Development Team"
__email__ = "<EMAIL>"

from .config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from .config.framework_configs import SupportedFrameworks
from .core.constellation_types import ConstellationType
from .core.constellation import ConstellationManager
from .core.temporal_state import TemporalStateManager
from .core.learning_hub import LearningHubCore

__all__ = [
    "UserProfile",
    "SkillLevel", 
    "LearningPace",
    "LearningStyle",
    "SupportedFrameworks",
    "ConstellationType",
    "ConstellationManager",
    "TemporalStateManager",
    "LearningHubCore",
]
