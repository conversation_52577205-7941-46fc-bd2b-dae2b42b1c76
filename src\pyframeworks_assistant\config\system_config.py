"""System configuration for GAAPF."""

import os
from typing import Optional
from pydantic import BaseSettings, Field


class SystemConfig(BaseSettings):
    """System configuration using environment variables."""
    
    # LLM API Keys
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    
    # Search and Tools
    tavily_api_key: Optional[str] = Field(None, env="TAVILY_API_KEY")
    
    # LangSmith Configuration
    langchain_tracing_v2: bool = Field(False, env="LANGCHAIN_TRACING_V2")
    langchain_api_key: Optional[str] = Field(None, env="LANGCHAIN_API_KEY")
    langchain_project: str = Field("gaapf-guidance-ai-agent", env="LANGCHAIN_PROJECT")
    
    # System Settings
    log_level: str = Field("INFO", env="GAAPF_LOG_LEVEL")
    max_concurrent_agents: int = Field(16, env="GAAPF_MAX_CONCURRENT_AGENTS")
    constellation_timeout: int = Field(300, env="GAAPF_CONSTELLATION_TIMEOUT")
    memory_cleanup_interval: int = Field(3600, env="GAAPF_MEMORY_CLEANUP_INTERVAL")
    
    # Development Settings
    debug: bool = Field(False, env="GAAPF_DEBUG")
    mock_responses: bool = Field(False, env="GAAPF_MOCK_RESPONSES")
    
    # File Paths
    user_profiles_dir: str = Field("user_profiles", env="GAAPF_USER_PROFILES_DIR")
    generated_code_dir: str = Field("generated_code", env="GAAPF_GENERATED_CODE_DIR")
    logs_dir: str = Field("logs", env="GAAPF_LOGS_DIR")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def has_llm_api_key(self) -> bool:
        """Check if at least one LLM API key is configured."""
        return any([
            self.google_api_key,
            self.openai_api_key,
            self.anthropic_api_key
        ])
    
    def get_primary_llm_provider(self) -> Optional[str]:
        """Get the primary LLM provider based on available API keys."""
        if self.google_api_key:
            return "google"
        elif self.openai_api_key:
            return "openai"
        elif self.anthropic_api_key:
            return "anthropic"
        return None
    
    def setup_langsmith(self) -> None:
        """Setup LangSmith tracing if configured."""
        if self.langchain_tracing_v2 and self.langchain_api_key:
            os.environ["LANGCHAIN_TRACING_V2"] = str(self.langchain_tracing_v2)
            os.environ["LANGCHAIN_API_KEY"] = self.langchain_api_key
            os.environ["LANGCHAIN_PROJECT"] = self.langchain_project


# Global system configuration instance
system_config = SystemConfig()
